#!/usr/bin/env python3
"""
诊断Docker容器问题
"""

import subprocess
import time
import json

def run_command(cmd):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Command timed out"
    except Exception as e:
        return -1, "", str(e)

def check_docker_status():
    """检查Docker状态"""
    print("🔍 检查Docker服务状态...")
    
    # 检查Docker是否运行
    code, stdout, stderr = run_command("docker --version")
    if code != 0:
        print("❌ Docker未安装或未运行")
        return False
    
    print(f"✅ Docker版本: {stdout.strip()}")
    
    # 检查容器状态
    print("\n📋 检查容器状态...")
    code, stdout, stderr = run_command("docker ps -a --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'")
    if code == 0:
        print(stdout)
    else:
        print(f"❌ 获取容器状态失败: {stderr}")
    
    return True

def get_container_logs(container_name, lines=50):
    """获取容器日志"""
    print(f"\n📜 获取 {container_name} 容器日志 (最近{lines}行)...")
    code, stdout, stderr = run_command(f"docker logs --tail {lines} {container_name}")
    
    if code == 0:
        if stdout.strip():
            print("--- 标准输出 ---")
            print(stdout)
        if stderr.strip():
            print("--- 错误输出 ---")
            print(stderr)
    else:
        print(f"❌ 获取日志失败: {stderr}")

def inspect_container(container_name):
    """检查容器详细信息"""
    print(f"\n🔍 检查 {container_name} 容器详细信息...")
    code, stdout, stderr = run_command(f"docker inspect {container_name}")
    
    if code == 0:
        try:
            data = json.loads(stdout)
            if data:
                container_info = data[0]
                state = container_info.get('State', {})
                config = container_info.get('Config', {})
                
                print(f"状态: {state.get('Status', 'Unknown')}")
                print(f"运行中: {state.get('Running', False)}")
                print(f"退出代码: {state.get('ExitCode', 'N/A')}")
                print(f"重启次数: {state.get('RestartCount', 0)}")
                
                if state.get('Error'):
                    print(f"错误: {state['Error']}")
                
                # 检查端口映射
                ports = container_info.get('NetworkSettings', {}).get('Ports', {})
                print(f"端口映射: {ports}")
                
                # 检查环境变量
                env_vars = config.get('Env', [])
                print("环境变量:")
                for env in env_vars:
                    if 'CUDA' in env or 'PATH' in env:
                        print(f"  {env}")
        except json.JSONDecodeError:
            print("❌ 解析容器信息失败")
    else:
        print(f"❌ 获取容器信息失败: {stderr}")

def restart_container(container_name):
    """重启容器"""
    print(f"\n🔄 重启 {container_name} 容器...")
    code, stdout, stderr = run_command(f"docker restart {container_name}")
    
    if code == 0:
        print(f"✅ {container_name} 重启成功")
        # 等待几秒钟让容器启动
        time.sleep(5)
        
        # 检查重启后的状态
        code, stdout, stderr = run_command(f"docker ps --filter name={container_name}")
        if code == 0:
            print("重启后状态:")
            print(stdout)
    else:
        print(f"❌ 重启失败: {stderr}")

def main():
    print("🐳 Docker容器诊断工具")
    print("=" * 50)
    
    if not check_docker_status():
        return
    
    containers = ["backend-omni-parser-1", "backend-image-embedding-1"]
    
    for container in containers:
        print(f"\n{'='*20} {container} {'='*20}")
        get_container_logs(container, 20)
        inspect_container(container)
        
        # 询问是否重启
        print(f"\n是否重启 {container}? (y/n): ", end="")
        try:
            choice = input().lower().strip()
            if choice == 'y':
                restart_container(container)
        except KeyboardInterrupt:
            print("\n操作被取消")
            break
    
    print("\n🏁 诊断完成")

if __name__ == "__main__":
    main()
