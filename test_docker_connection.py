#!/usr/bin/env python3
"""
测试Docker后端服务连接
"""

import requests
import time

def test_docker_services():
    """测试Docker后端服务"""
    services = [
        ("OmniParser", "http://127.0.0.1:8000/"),
        ("ImageEmbedding", "http://127.0.0.1:8001/")
    ]
    
    for service_name, url in services:
        print(f"🔍 测试 {service_name} 服务: {url}")
        try:
            response = requests.get(url, timeout=5)
            print(f"✅ {service_name} 服务正常 - 状态码: {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"❌ {service_name} 服务连接失败 - 连接被拒绝")
        except requests.exceptions.Timeout:
            print(f"⏰ {service_name} 服务超时")
        except Exception as e:
            print(f"❌ {service_name} 服务错误: {str(e)}")
        print()

def test_omni_parser_endpoint():
    """测试OmniParser的process_image端点"""
    url = "http://127.0.0.1:8000/process_image/"
    print(f"🔍 测试 OmniParser process_image 端点: {url}")
    
    try:
        # 尝试POST请求（不带文件，只是测试端点是否存在）
        response = requests.post(url, timeout=5)
        print(f"✅ process_image 端点可访问 - 状态码: {response.status_code}")
        if response.status_code == 422:
            print("💡 端点正常，但需要文件参数（这是预期的）")
    except requests.exceptions.ConnectionError:
        print(f"❌ process_image 端点连接失败")
    except Exception as e:
        print(f"❌ process_image 端点错误: {str(e)}")

if __name__ == "__main__":
    print("🧪 开始测试Docker后端服务连接...")
    print("=" * 50)
    
    test_docker_services()
    test_omni_parser_endpoint()
    
    print("🏁 测试完成")
