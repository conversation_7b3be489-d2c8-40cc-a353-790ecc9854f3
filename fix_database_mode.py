#!/usr/bin/env python3
"""
修复数据库模式，让系统使用智能匹配而不是备用模式
"""

import sys
import traceback
import config

def test_neo4j_connection():
    """测试Neo4j连接"""
    print("🔍 测试Neo4j连接...")
    
    try:
        from data.graph_db import Neo4jDatabase
        
        print(f"   URI: {config.Neo4j_URI}")
        print(f"   用户名: {config.Neo4j_AUTH[0]}")
        
        db = Neo4jDatabase(config.Neo4j_URI, config.Neo4j_AUTH)
        print("✅ Neo4j连接成功!")
        
        # 测试基本查询
        with db.driver.session() as session:
            result = session.run("RETURN 'Hello Neo4j' as message")
            record = result.single()
            print(f"✅ 查询测试成功: {record['message']}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Neo4j连接失败: {str(e)}")
        print("🔍 详细错误:")
        traceback.print_exc()
        return False

def check_database_schema():
    """检查数据库结构"""
    print("\n🔍 检查数据库结构...")
    
    try:
        from data.graph_db import Neo4jDatabase
        
        db = Neo4jDatabase(config.Neo4j_URI, config.Neo4j_AUTH)
        
        with db.driver.session() as session:
            # 检查节点标签
            result = session.run("CALL db.labels()")
            labels = [record["label"] for record in result]
            print(f"📋 现有节点标签: {labels}")
            
            # 检查关系类型
            result = session.run("CALL db.relationshipTypes()")
            relationships = [record["relationshipType"] for record in result]
            print(f"🔗 现有关系类型: {relationships}")
            
            # 检查Action节点数量
            result = session.run("MATCH (a:Action) RETURN count(a) as count")
            action_count = result.single()["count"]
            print(f"🎯 Action节点数量: {action_count}")
            
            # 检查Element节点数量
            result = session.run("MATCH (e:Element) RETURN count(e) as count")
            element_count = result.single()["count"]
            print(f"📱 Element节点数量: {element_count}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库结构检查失败: {str(e)}")
        return False

def initialize_basic_data():
    """初始化基本数据"""
    print("\n🔧 初始化基本数据...")
    
    try:
        from data.graph_db import Neo4jDatabase
        
        db = Neo4jDatabase(config.Neo4j_URI, config.Neo4j_AUTH)
        
        with db.driver.session() as session:
            # 创建基本的Action节点
            basic_actions = [
                {
                    "action_id": "open_calendar_1",
                    "task": "打开日历",
                    "action_type": "tap",
                    "description": "点击日历应用图标",
                    "success_rate": 0.9
                },
                {
                    "action_id": "open_wechat_1", 
                    "task": "打开微信",
                    "action_type": "tap",
                    "description": "点击微信应用图标",
                    "success_rate": 0.9
                },
                {
                    "action_id": "search_app_1",
                    "task": "搜索应用",
                    "action_type": "tap",
                    "description": "点击搜索框",
                    "success_rate": 0.8
                }
            ]
            
            for action in basic_actions:
                query = """
                MERGE (a:Action {action_id: $action_id})
                SET a.task = $task,
                    a.action_type = $action_type,
                    a.description = $description,
                    a.success_rate = $success_rate,
                    a.created_at = datetime()
                """
                session.run(query, action)
                print(f"✅ 创建Action节点: {action['action_id']}")
            
            # 创建基本的Element节点
            basic_elements = [
                {
                    "element_id": "calendar_icon_1",
                    "text": "日历",
                    "element_type": "icon",
                    "app_name": "Calendar"
                },
                {
                    "element_id": "wechat_icon_1",
                    "text": "微信", 
                    "element_type": "icon",
                    "app_name": "WeChat"
                },
                {
                    "element_id": "search_box_1",
                    "text": "搜索",
                    "element_type": "input",
                    "app_name": "Launcher"
                }
            ]
            
            for element in basic_elements:
                query = """
                MERGE (e:Element {element_id: $element_id})
                SET e.text = $text,
                    e.element_type = $element_type,
                    e.app_name = $app_name,
                    e.created_at = datetime()
                """
                session.run(query, element)
                print(f"✅ 创建Element节点: {element['element_id']}")
            
            # 创建关系
            relationships = [
                ("open_calendar_1", "calendar_icon_1"),
                ("open_wechat_1", "wechat_icon_1"),
                ("search_app_1", "search_box_1")
            ]
            
            for action_id, element_id in relationships:
                query = """
                MATCH (a:Action {action_id: $action_id})
                MATCH (e:Element {element_id: $element_id})
                MERGE (a)-[:TARGETS]->(e)
                """
                session.run(query, {"action_id": action_id, "element_id": element_id})
                print(f"✅ 创建关系: {action_id} -> {element_id}")
        
        db.close()
        print("✅ 基本数据初始化完成!")
        return True
        
    except Exception as e:
        print(f"❌ 数据初始化失败: {str(e)}")
        traceback.print_exc()
        return False

def test_data_retrieval():
    """测试数据检索"""
    print("\n🧪 测试数据检索...")
    
    try:
        from data.graph_db import Neo4jDatabase
        
        db = Neo4jDatabase(config.Neo4j_URI, config.Neo4j_AUTH)
        
        # 测试get_all_actions方法
        actions = db.get_all_actions()
        print(f"✅ 检索到 {len(actions)} 个Action节点")
        
        if actions:
            print("📋 Action节点示例:")
            for i, action in enumerate(actions[:3]):
                print(f"   {i+1}. {action.get('action_id', 'N/A')} - {action.get('task', 'N/A')}")
        
        db.close()
        return len(actions) > 0
        
    except Exception as e:
        print(f"❌ 数据检索测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 修复数据库模式，启用智能匹配...")
    print("=" * 60)
    
    success_count = 0
    total_tests = 4
    
    # 1. 测试连接
    if test_neo4j_connection():
        success_count += 1
    
    # 2. 检查结构
    if check_database_schema():
        success_count += 1
    
    # 3. 初始化数据
    if initialize_basic_data():
        success_count += 1
    
    # 4. 测试检索
    if test_data_retrieval():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 完成情况: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 数据库模式修复成功!")
        print("💡 现在系统将使用智能匹配模式而不是备用模式")
        print("\n下次执行任务时，您应该看到:")
        print("   ✅ 从数据库检索到历史操作")
        print("   ✅ 使用视觉嵌入匹配元素")
        print("   ✅ 执行精确的模板操作")
    else:
        print("⚠️ 部分修复失败，系统可能仍会使用备用模式")
        print("💡 备用模式虽然不如智能匹配精确，但仍能完成基本任务")

if __name__ == "__main__":
    main()
