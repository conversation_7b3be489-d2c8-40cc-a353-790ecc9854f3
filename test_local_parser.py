#!/usr/bin/env python3
"""
测试本地解析器是否工作
"""

import sys
import os
import traceback

def test_local_parser():
    """测试本地解析器"""
    print("🧪 测试本地屏幕解析器...")
    
    try:
        # 1. 测试导入
        print("1. 测试导入本地解析器...")
        from local_screen_parser import parse_screen_local
        print("✅ 本地解析器导入成功")
        
        # 2. 测试OpenCV
        print("2. 测试OpenCV...")
        import cv2
        print(f"✅ OpenCV版本: {cv2.__version__}")
        
        # 3. 测试PIL
        print("3. 测试PIL...")
        from PIL import Image
        print("✅ PIL导入成功")
        
        # 4. 测试screen_element工具
        print("4. 测试修改后的screen_element工具...")
        from tool.screen_content import screen_element
        print("✅ screen_element工具导入成功")
        
        # 5. 创建一个测试图片
        print("5. 创建测试图片...")
        test_img = Image.new('RGB', (1080, 1920), color='white')
        test_path = "test_screenshot.png"
        test_img.save(test_path)
        print(f"✅ 测试图片已创建: {test_path}")
        
        # 6. 测试本地解析
        print("6. 测试本地解析功能...")
        result = parse_screen_local(test_path)
        print(f"✅ 本地解析成功，发现 {result.get('total_elements', 0)} 个元素")
        
        # 7. 测试screen_element工具
        print("7. 测试screen_element工具...")
        tool_result = screen_element.invoke({"image_path": test_path})
        
        if "error" not in tool_result:
            print("✅ screen_element工具测试成功!")
            print(f"   - 状态: {tool_result.get('status')}")
            print(f"   - 解析方法: {tool_result.get('parsing_method')}")
            print(f"   - JSON路径: {tool_result.get('parsed_content_json_path')}")
        else:
            print(f"❌ screen_element工具测试失败: {tool_result['error']}")
            return False
        
        # 清理测试文件
        if os.path.exists(test_path):
            os.remove(test_path)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        print("🔍 详细错误:")
        traceback.print_exc()
        return False

def test_dependencies():
    """测试依赖是否安装"""
    print("📦 检查依赖...")
    
    deps = ['cv2', 'PIL', 'numpy']
    missing = []
    
    for dep in deps:
        try:
            if dep == 'cv2':
                import cv2
                print(f"✅ OpenCV: {cv2.__version__}")
            elif dep == 'PIL':
                from PIL import Image
                print("✅ PIL: 已安装")
            elif dep == 'numpy':
                import numpy as np
                print(f"✅ NumPy: {np.__version__}")
        except ImportError:
            print(f"❌ {dep}: 未安装")
            missing.append(dep)
    
    if missing:
        print(f"\n⚠️ 缺少依赖: {missing}")
        print("请运行: pip install opencv-python pillow numpy")
        return False
    
    return True

def main():
    """主函数"""
    print("🎯 本地屏幕解析器测试")
    print("=" * 50)
    
    # 检查依赖
    if not test_dependencies():
        return False
    
    # 测试解析器
    if test_local_parser():
        print("\n🎉 所有测试通过!")
        print("✅ 本地屏幕解析器已准备就绪")
        print("🚀 现在可以重新测试'打开计算器'功能")
        return True
    else:
        print("\n❌ 测试失败")
        return False

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程出错: {e}")
        traceback.print_exc()
