FROM pytorch/pytorch:2.2.0-cuda12.1-cudnn8-runtime

WORKDIR /app

# 安装基本的系统依赖
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgtk-3-0 \
    && rm -rf /var/lib/apt/lists/*

# 设置环境变量以避免 NumPy 问题
ENV NUMPY_EXPERIMENTAL_ARRAY_FUNCTION=0
ENV OMP_NUM_THREADS=1

# 先安装 NumPy 的特定版本
RUN pip install --no-cache-dir numpy==1.24.3

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

# 创建模型和权重文件夹
RUN mkdir -p weights/icon_detect_v1_5 \
    && mkdir -p weights/icon_caption_florence

EXPOSE 8000

CMD ["uvicorn", "omni:app", "--host", "0.0.0.0", "--port", "8000"] 