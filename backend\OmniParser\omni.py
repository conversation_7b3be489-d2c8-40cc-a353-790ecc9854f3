import os
import tempfile
import time
from fastapi import FastAPI, File, UploadFile, Form
from fastapi.responses import JSONResponse
from utils import get_som_labeled_img, check_ocr_box, get_caption_model_processor, get_yolo_model
from PIL import Image
import io
import base64
import torch
import pandas as pd
import uvicorn

# 初始化 FastAPI
app = FastAPI()

# 自动检测可用设备
device = 'cuda:0' if torch.cuda.is_available() else 'cpu'
print(f"Using device: {device}")

# 初始化模型，只加载一次
yolo_model_path = 'weights/icon_detect_v1_5/best.pt'
caption_model_name = 'florence2'
caption_model_path = 'weights/icon_caption_florence'

# 检查模型文件是否存在
models_available = os.path.exists(yolo_model_path)
print(f"Models available: {models_available}")

if models_available:
    try:
        print("🔄 加载 YOLO 模型...")
        som_model = get_yolo_model(model_path=yolo_model_path)
        som_model.to(device)
        print("✅ YOLO 模型加载成功")

        print("🔄 加载 Florence2 模型...")
        caption_model_processor = get_caption_model_processor(
            model_name=caption_model_name,
            model_name_or_path=caption_model_path,
            device=device
        )
        print("✅ Florence2 模型加载成功")

    except Exception as e:
        print(f"❌ 模型加载失败: {str(e)}")
        print("⚠️ 将使用基本的备用处理方式")
        som_model = None
        caption_model_processor = None
        models_available = False
else:
    som_model = None
    caption_model_processor = None
    print("⚠️ 模型文件不存在，将使用基本的备用处理方式")

@app.post("/process_image/")
async def process_image(
    file: UploadFile = File(...),
    box_threshold: float = Form(0.05), # Box Threshold
    iou_threshold: float = Form(0.1),  # IOU Threshold
    imgsz_component: int = Form(640)  # Icon Detect Image Size
):
    try:
        # 检查模型是否可用
        if not models_available:
            # 读取上传的图片并转换为base64作为标注图像
            contents = await file.read()
            labeled_image_b64 = base64.b64encode(contents).decode('utf-8')

            return JSONResponse(
                status_code=200,
                content={
                    "status": "success",
                    "message": "Models not available, using fallback processing",
                    "parsed_content": _get_fallback_elements(),
                    "labeled_image": labeled_image_b64,
                    "e_time": 0.1
                }
            )

        # 保存上传文件到临时路径
        contents = await file.read()
        temp_dir = tempfile.mkdtemp()
        temp_image_path = os.path.join(temp_dir, file.filename)

        with open(temp_image_path, 'wb') as f:
            f.write(contents)


        image = Image.open(temp_image_path).convert('RGB')
        start_time = time.time()
        # OCR 检测
        ocr_bbox_rslt, _ = check_ocr_box(
            temp_image_path,
            display_img=False,
            output_bb_format='xyxy',
            goal_filtering=None,
            easyocr_args={'paragraph': False, 'text_threshold': 0.5},
            use_paddleocr=True
        )
        text, ocr_bbox = ocr_bbox_rslt
        
        # 生成标注图像和解析内容
        draw_bbox_config = {
            'text_scale': 0.8 * (max(image.size) / 3200),
            'text_thickness': max(int(2 * (max(image.size) / 3200)), 1),
            'text_padding': max(int(3 * (max(image.size) / 3200)), 1),
            'thickness': max(int(3 * (max(image.size) / 3200)), 1),
        }
        
        dino_labeled_img, label_coordinates, parsed_content_list = get_som_labeled_img(
            temp_image_path,
            som_model,
            BOX_TRESHOLD=box_threshold,
            output_coord_in_ratio=True,
            ocr_bbox=ocr_bbox,
            draw_bbox_config=draw_bbox_config,
            caption_model_processor=caption_model_processor,
            ocr_text=text,
            use_local_semantics=True,
            iou_threshold=iou_threshold,
            scale_img=False,
            batch_size=128,
            imgsz=imgsz_component
        )
        elapsed_time = time.time() - start_time
        # 删除临时文件
        os.remove(temp_image_path)
        os.rmdir(temp_dir)
        

        # 返回标注图片和解析内容
        image_bytes = base64.b64decode(dino_labeled_img)
        labeled_image = io.BytesIO(image_bytes)

        # 解析内容转 DataFrame -> JSON
        df = pd.DataFrame(parsed_content_list)
        df['ID'] = range(len(df))
        parsed_content_json = df.to_dict(orient="records")

        # base64 编码
        encoded_image = base64.b64encode(labeled_image.getvalue())
        

        return {
            "status": "success",
            "parsed_content": parsed_content_json,
            "labeled_image": encoded_image,
            "e_time": elapsed_time  # 返回耗时
        }

    except Exception as e:
        return JSONResponse(status_code=500, content={"error": str(e)})


def _get_fallback_elements():
    """当模型不可用时返回基本的UI元素"""
    return [
        {
            "ID": 0,
            "text": "屏幕中心区域",
            "bbox": [400, 600, 800, 1000],
            "center": [600, 800],
            "clickable": True,
            "type": "fallback_center"
        },
        {
            "ID": 1,
            "text": "返回按钮区域",
            "bbox": [50, 50, 150, 150],
            "center": [100, 100],
            "clickable": True,
            "type": "fallback_back"
        },
        {
            "ID": 2,
            "text": "底部导航区域",
            "bbox": [200, 1800, 800, 1900],
            "center": [500, 1850],
            "clickable": True,
            "type": "fallback_navigation"
        }
    ]


# nohup fastapi run omni.py --port 8000 > ../logfile_omni.log 2>&1