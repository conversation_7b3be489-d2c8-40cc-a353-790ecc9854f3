#!/usr/bin/env python3
"""
测试Docker服务连接
"""

import requests
import time
import json

def test_service_connection(service_name, url, timeout=10):
    """测试服务连接"""
    print(f"🔍 测试 {service_name}: {url}")
    
    try:
        response = requests.get(url, timeout=timeout)
        print(f"✅ {service_name} 连接成功")
        print(f"   状态码: {response.status_code}")
        print(f"   响应内容: {response.text[:200]}...")
        return True
    except requests.exceptions.ConnectionError as e:
        print(f"❌ {service_name} 连接失败: {str(e)}")
        return False
    except requests.exceptions.Timeout:
        print(f"⏰ {service_name} 连接超时")
        return False
    except Exception as e:
        print(f"❌ {service_name} 其他错误: {str(e)}")
        return False

def test_omni_parser_endpoint():
    """测试OmniParser的具体端点"""
    url = "http://127.0.0.1:8000/process_image/"
    print(f"\n🔍 测试 OmniParser process_image 端点: {url}")
    
    try:
        # 发送一个空的POST请求来测试端点是否存在
        response = requests.post(url, timeout=10)
        print(f"✅ process_image 端点可访问")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 422:
            print("💡 端点正常，返回422是因为缺少必需的文件参数（这是预期的）")
            return True
        elif response.status_code == 200:
            print("✅ 端点完全正常")
            return True
        else:
            print(f"⚠️ 端点返回了意外的状态码: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError as e:
        print(f"❌ process_image 端点连接失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ process_image 端点错误: {str(e)}")
        return False

def test_with_sample_image():
    """使用示例图片测试OmniParser"""
    import os
    from PIL import Image
    import io
    
    # 创建一个简单的测试图片
    test_image_path = "test_image.png"
    
    try:
        # 创建一个简单的测试图片
        img = Image.new('RGB', (100, 100), color='red')
        img.save(test_image_path)
        
        print(f"\n🧪 使用测试图片测试 OmniParser...")
        
        url = "http://127.0.0.1:8000/process_image/"
        
        with open(test_image_path, 'rb') as f:
            files = {'file': ('test.png', f, 'image/png')}
            response = requests.post(url, files=files, timeout=30)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ OmniParser 图片处理成功!")
            try:
                result = response.json()
                print(f"   返回数据键: {list(result.keys())}")
                if 'status' in result:
                    print(f"   处理状态: {result['status']}")
            except:
                print("   响应不是JSON格式")
        else:
            print(f"❌ 图片处理失败")
            print(f"   响应内容: {response.text[:500]}")
        
        # 清理测试文件
        if os.path.exists(test_image_path):
            os.remove(test_image_path)
            
    except Exception as e:
        print(f"❌ 测试图片处理时出错: {str(e)}")
        # 清理测试文件
        if os.path.exists(test_image_path):
            os.remove(test_image_path)

def main():
    print("🧪 测试Docker后端服务")
    print("=" * 50)
    
    # 测试基本连接
    services = [
        ("OmniParser 根路径", "http://127.0.0.1:8000/"),
        ("ImageEmbedding 根路径", "http://127.0.0.1:8001/"),
    ]
    
    all_good = True
    for service_name, url in services:
        if not test_service_connection(service_name, url):
            all_good = False
        print()
    
    # 测试具体端点
    if not test_omni_parser_endpoint():
        all_good = False
    
    # 如果基本连接都正常，测试实际功能
    if all_good:
        test_with_sample_image()
    
    print("\n" + "="*50)
    if all_good:
        print("✅ 所有服务测试通过！")
        print("💡 如果仍有连接问题，可能是防火墙或网络配置问题")
    else:
        print("❌ 部分服务测试失败")
        print("💡 建议检查Docker容器日志和网络配置")

if __name__ == "__main__":
    main()
