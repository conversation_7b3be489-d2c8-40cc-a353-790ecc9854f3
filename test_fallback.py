#!/usr/bin/env python3
"""
测试 OmniParser 的备用处理功能
"""

import requests
import time
from PIL import Image
import io
import base64

def test_fallback_processing():
    """测试备用处理功能"""
    print("🧪 测试 OmniParser 备用处理功能...")
    
    # 创建一个简单的测试图片
    img = Image.new('RGB', (200, 200), color='blue')
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='PNG')
    img_bytes.seek(0)
    
    url = "http://127.0.0.1:8000/process_image/"
    
    max_retries = 5
    for attempt in range(max_retries):
        try:
            print(f"🔄 尝试 {attempt + 1}/{max_retries}...")
            
            files = {'file': ('test.png', img_bytes.getvalue(), 'image/png')}
            response = requests.post(url, files=files, timeout=30)
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 请求成功!")
                print(f"   状态: {result.get('status')}")
                print(f"   消息: {result.get('message')}")
                print(f"   解析元素数量: {len(result.get('parsed_content', []))}")
                print(f"   是否有标注图像: {'是' if result.get('labeled_image') else '否'}")
                print(f"   处理时间: {result.get('e_time')}秒")
                
                # 检查解析内容
                parsed_content = result.get('parsed_content', [])
                if parsed_content:
                    print("\n📋 解析的UI元素:")
                    for i, element in enumerate(parsed_content[:3]):  # 只显示前3个
                        print(f"   {i+1}. {element.get('text', 'N/A')} - 类型: {element.get('type', 'N/A')}")
                
                return True
                
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"   响应: {response.text}")
                
        except requests.exceptions.ConnectionError:
            print(f"❌ 连接失败 (尝试 {attempt + 1}/{max_retries})")
            if attempt < max_retries - 1:
                print("⏳ 等待5秒后重试...")
                time.sleep(5)
        except Exception as e:
            print(f"❌ 其他错误: {str(e)}")
            
        # 重置图片字节流位置
        img_bytes.seek(0)
    
    print("❌ 所有尝试都失败了")
    return False

def check_container_status():
    """检查容器状态"""
    print("🔍 检查容器状态...")
    
    import subprocess
    try:
        result = subprocess.run(['docker', 'ps', '--filter', 'name=backend-omni-parser-1'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            if len(lines) > 1:  # 有标题行和数据行
                print("✅ 容器正在运行")
                return True
            else:
                print("❌ 容器未运行")
                return False
        else:
            print("❌ 无法检查容器状态")
            return False
    except Exception as e:
        print(f"❌ 检查容器状态时出错: {str(e)}")
        return False

def main():
    print("🚀 开始测试 OmniParser 备用处理...")
    print("=" * 50)
    
    # 检查容器状态
    if not check_container_status():
        print("💡 请先启动容器: docker-compose up -d")
        return
    
    # 等待一会儿让容器完全启动
    print("⏳ 等待容器完全启动...")
    time.sleep(10)
    
    # 测试备用处理
    if test_fallback_processing():
        print("\n🎉 备用处理测试成功!")
        print("💡 现在可以尝试运行 '打开相册' 任务了")
    else:
        print("\n❌ 备用处理测试失败")
        print("💡 建议检查容器日志: docker logs backend-omni-parser-1")

if __name__ == "__main__":
    main()
